import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ThemedButton } from '@/components/ui/themed-button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, FileText, Upload, CheckCircle } from 'lucide-react';
import { createClient } from '@/app/utils/supabase/client';
import { Resume, ResumeAnalysisResult } from '@/app/types/globalTypes';
import { toast } from '@/hooks/use-toast';

interface UploadResumeFormProps {
  userId?: string;
  allowAnonymous?: boolean;
  onComplete: (resume: Resume) => void;
}

const UploadResumeForm: React.FC<UploadResumeFormProps> = ({
  userId,
  onComplete
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const supabase = createClient();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Validate file type
      const fileExt = selectedFile.name.split('.').pop()?.toLowerCase();
      if (!fileExt || !['pdf', 'docx', 'doc'].includes(fileExt)) {
        toast({
          title: 'Invalid file format',
          description: 'Please upload a PDF or DOCX file',
          variant: 'destructive'
        });
        return;
      }

      // Validate file size (max 5MB)
      if (selectedFile.size > 5 * 1024 * 1024) {
        toast({
          title: 'File too large',
          description: 'Maximum file size is 5MB',
          variant: 'destructive'
        });
        return;
      }

      setFile(selectedFile);
    }
  };

  const handleUpload = useCallback(async () => {
    if (!file) return;

    try {
      setIsUploading(true);
      setUploadProgress(10);

      if (userId) {
        // Create a filename: timestamp_originalname with no spaces
        const timestamp = new Date().getTime();
        const fileName = `${timestamp}_${file.name.replace(/\s+/g, '_')}`;
        const fileType = file.name.split('.').pop()?.toLowerCase();

        // Upload file to Supabase Storage
        setUploadProgress(30);
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('private-documents')
          .upload(`${userId}/${fileName}`, file, {
            cacheControl: '3600',
            upsert: false
          });

        console.log('Upload data:', uploadData);
        console.log('Upload error:', uploadError);

        if (uploadError) {
          throw new Error(`File upload failed: ${uploadError.message}`);
        }

        setUploadProgress(60);

        // Create resume record in database
        const { data: resumeData, error: resumeError } = await supabase
          .from('user_resumes')
          .insert({
            user_id: userId,
            resume: uploadData.path,
            file_name: file.name,
            file_type: fileType
          })
          .select('*')
          .single();

        if (resumeError) {
          throw new Error(`CV record creation failed: ${resumeError.message}`);
        }

        setUploadProgress(100);
        setIsCompleted(true);

        // Slight delay to show the completion state
        setTimeout(() => {
          onComplete(resumeData as Resume);
        }, 500);
      } else {
        // No userId: store resume locally
        const reader = new FileReader();
        reader.onload = () => {
          const fileContent = reader.result as string;
          // Define a type for local resume without isLocal property
          type LocalResume = Omit<Resume, 'isLocal'> & { isLocal?: boolean };
          const localResume: LocalResume = {
            id: `local-${Date.now()}`,
            user_id: '',
            resume: fileContent,
            file_name: file.name,
            file_type: file.name.split('.').pop()?.toLowerCase() || '',
            isLocal: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            thumbnail_url: '',
            analysis_result: { sections: [] } as ResumeAnalysisResult
          };
          // Save to localStorage
          const stored = localStorage.getItem('localResumes');
          const localResumes = stored ? JSON.parse(stored) : [];

          // Limit the number of stored local resumes to avoid exceeding quota
          const MAX_LOCAL_RESUMES = 5;
          if (localResumes.length >= MAX_LOCAL_RESUMES) {
            localResumes.shift(); // Remove oldest resume
          }

          localResumes.push(localResume);

          try {
            // Try to save, if quota exceeded, remove oldest and retry once
            localStorage.setItem('localResumes', JSON.stringify(localResumes));
          } catch (e) {
            console.warn(
              'Storage quota exceeded, attempting to free space and retry:',
              e
            );
            if (localResumes.length > 1) {
              localResumes.shift(); // Remove oldest resume
              try {
                localStorage.setItem(
                  'localResumes',
                  JSON.stringify(localResumes)
                );
              } catch (e2) {
                console.error('Failed to save local resume after retry:', e2);
                toast({
                  title: 'Storage limit reached',
                  description:
                    'Cannot save more local resumes. Please sign in to save permanently.',
                  variant: 'destructive'
                });
              }
            } else {
              console.error(
                'Failed to save local resume due to storage quota:',
                e
              );
              toast({
                title: 'Storage limit reached',
                description:
                  'Cannot save more local resumes. Please sign in to save permanently.',
                variant: 'destructive'
              });
            }
          }

          setIsCompleted(true);
          setUploadProgress(100);
          onComplete(localResume);
        };
        reader.readAsDataURL(file);
      }
    } catch (error) {
      console.error('CV upload failed:', error);
      toast({
        title: 'Upload Failed',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive'
      });
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [file, userId, supabase, onComplete]);

  return (
    <div className="space-y-6">
      {!isUploading ? (
        <>
          <Card className="border-dashed border-white/30 backdrop-blur-sm bg-white/10 shadow-lg">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center py-10 space-y-4">
                {file ? (
                  <div className="flex items-center gap-2 text-[hsl(var(--hero-yellow))]">
                    <FileText className="h-8 w-8" />
                    <span className="font-medium">{file.name}</span>
                  </div>
                ) : (
                  <div className="flex flex-col items-center gap-4">
                    <div className="p-6 bg-white/10 rounded-full border border-white/20">
                      <Upload className="h-10 w-10 text-[hsl(var(--hero-yellow))]" />
                    </div>
                    <div className="text-center">
                      <p className="font-medium text-lg text-white">
                        Drag & drop your CV
                      </p>
                      <p className="text-sm text-slate-300">
                        or click to browse files (PDF, DOCX)
                      </p>
                    </div>
                  </div>
                )}

                <Input
                  id="resume-upload"
                  type="file"
                  accept=".pdf,.docx,.doc"
                  onChange={handleFileChange}
                  className={
                    file
                      ? 'hidden'
                      : 'opacity-0 absolute inset-0 w-full h-full cursor-pointer'
                  }
                />

                {file && (
                  <div className="flex gap-3">
                    <Button
                      variant="ghost"
                      onClick={() => setFile(null)}
                      className="border-white/30 text-white hover:bg-white/10 hover:text-white hover:border-white/50 transition-all"
                    >
                      Change File
                    </Button>
                    <ThemedButton onClick={handleUpload} variant="primary">
                      <Upload className="h-4 w-4" />
                      Upload CV
                    </ThemedButton>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="text-sm text-slate-300">
            <p>Supported formats: PDF, DOCX. Maximum file size: 5MB.</p>
          </div>
        </>
      ) : (
        <div className="flex flex-col items-center justify-center py-10 space-y-6 backdrop-blur-sm bg-white/5 border border-white/20 rounded-lg shadow-lg p-8">
          {isCompleted ? (
            <>
              <div className="bg-[hsl(var(--hero-yellow))]/20 rounded-full p-4 border border-[hsl(var(--hero-yellow))]/30 shadow-[0_0_15px_rgba(246,160,60,0.3)]">
                <CheckCircle className="h-12 w-12 text-[hsl(var(--hero-yellow))]" />
              </div>
              <div className="text-center space-y-2">
                <h3 className="text-xl font-medium text-white">
                  Upload Complete!
                </h3>
                <p className="text-slate-300">
                  Your CV has been uploaded successfully.
                </p>
              </div>
            </>
          ) : (
            <>
              <Loader2 className="h-12 w-12 text-[hsl(var(--hero-yellow))] animate-spin" />
              <div className="text-center space-y-2">
                <h3 className="text-xl font-medium text-white">
                  Uploading CV...
                </h3>
                <p className="text-slate-300">
                  {uploadProgress < 60
                    ? 'Uploading your file to our secure storage'
                    : 'Processing your CV'}
                </p>
              </div>
              <div className="w-full max-w-xs">
                <div className="h-3 w-full bg-white/10 rounded-full overflow-hidden">
                  <div
                    className="h-3 bg-[hsl(var(--hero-yellow))] rounded-full transition-all duration-300 shadow-[0_0_8px_rgba(246,160,60,0.5)]"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <div className="flex justify-end mt-2">
                  <span className="text-sm text-white">{uploadProgress}%</span>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default UploadResumeForm;
