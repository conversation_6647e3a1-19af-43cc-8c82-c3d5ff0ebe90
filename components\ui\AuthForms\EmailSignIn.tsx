'use client';

import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { signInWithEmail } from '@/app/utils/auth-helpers/server';
import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Input } from '../input';
import FormAlert from './FormAlert';

// Define prop type with allowPassword boolean
interface EmailSignInProps {
  allowPassword: boolean;
  redirectMethod: string;
  disableButton?: boolean;
  next?: string;
}

export default function EmailSignIn({
  allowPassword,
  disableButton,
  next
}: Readonly<EmailSignInProps>) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const searchParams = useSearchParams();

  // Get status and error messages from URL parameters
  const status = searchParams.get('status');
  const statusDescription = searchParams.get('status_description');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');
  const isFromSignup = searchParams.get('signup') === 'true';

  // Reset loading state on mount and clear on unmount
  useEffect(() => {
    // Reset loading state when component mounts
    setIsSubmitting(false);

    // Clear loading state when component unmounts
    return () => {
      if (isSubmitting) {
        console.log('Clearing loading state on unmount');
        setIsSubmitting(false);
      }
    };
  }, [isSubmitting]);

  // Set up safety timeout to clear loading state
  useEffect(() => {
    let safetyTimeout: NodeJS.Timeout | null = null;

    if (isSubmitting) {
      safetyTimeout = setTimeout(() => {
        console.log('Safety timeout triggered to clear loading state');
        setIsSubmitting(false);
      }, 5000); // 5 seconds max wait time
    }

    return () => {
      if (safetyTimeout) {
        clearTimeout(safetyTimeout);
      }
    };
  }, [isSubmitting]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // If already submitting, don't allow another submission
    if (isSubmitting) return;

    setIsSubmitting(true);

    // Create a local variable to track if we should update state
    // This helps prevent state updates after component unmount
    let isMounted = true;

    try {
      // Use a direct approach instead of handleRequest
      const formData = new FormData(e.currentTarget);

      // Get next parameter from URL if present
      const nextParam =
        next || new URLSearchParams(window.location.search).get('next');
      if (nextParam) {
        formData.append('next', nextParam);
      }

      // Call the server action directly
      const redirectPath = await signInWithEmail(formData);

      // Clear any logged out flag
      try {
        sessionStorage.removeItem('logged_out');
      } catch (e) {
        console.error('Error clearing sessionStorage:', e);
      }

      // Navigate immediately to avoid getting stuck
      console.log('Redirecting to:', redirectPath);
      window.location.replace(redirectPath);

      // Don't clear loading state since we're navigating away
    } catch (error) {
      console.error('Error during email sign-in:', error);

      if (isMounted) {
        setIsSubmitting(false);
      }
    }

    // Set cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  };

  return (
    <div className="my-4 text-white">
      {/* Use FormAlert component */}
      <FormAlert
        status={status}
        statusDescription={statusDescription}
        error={error}
        errorDescription={errorDescription}
      />

      {/* Show additional context for users coming from signup */}
      {isFromSignup && !error && (
        <div className="mb-4 p-3 bg-blue-50/20 backdrop-blur-sm border border-blue-200/30 rounded-md">
          <p className="text-sm text-blue-100">
            ✨ <strong>Account created successfully!</strong> Once you confirm
            your email, you can sign in below or use the magic link option.
          </p>
        </div>
      )}

      <form
        noValidate={true}
        className="mb-4"
        onSubmit={(e) => handleSubmit(e)}
      >
        <div className="grid gap-2">
          <div className="grid gap-1">
            <label htmlFor="email" className="text-white">
              Email
            </label>
            <Input
              id="email"
              placeholder="<EMAIL>"
              type="email"
              name="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              className="w-full p-3 rounded-md bg-white/10 border-white/20 text-white auth-input"
            />
          </div>
          <Button
            type="submit"
            className="mt-1 bg-[hsl(var(--hero-yellow))] hover:bg-[hsl(var(--hero-yellow-light))] text-[hsl(var(--foreground))]"
            loading={isSubmitting}
            disabled={disableButton}
          >
            Sign in
          </Button>
        </div>
      </form>
      {allowPassword && (
        <>
          <p>
            <Link
              href={`/signin/password_signin?next=${encodeURIComponent(next ?? '/dashboard')}`}
              className="font-light text-sm text-[hsl(var(--hero-yellow))] hover:underline"
            >
              Sign in with email and password
            </Link>
          </p>
          <p>
            <Link
              href={`/signin/signup?next=${encodeURIComponent(next ?? '/dashboard')}`}
              className="font-light text-sm text-[hsl(var(--hero-yellow))] hover:underline"
            >
              Don&apos;t have an account? Sign up
            </Link>
          </p>
        </>
      )}
    </div>
  );
}
