// components/home/<USER>
'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { Check, Target } from '@/lib/icons';

const HeroBelowFold: React.FC = () => {
  const [currentExample, setCurrentExample] = useState(0);

  // Simplified examples array (reduced from 4 to 2 for performance)
  const examples = [
    {
      profession: 'Marketing Manager',
      company: 'Digital Agency',
      beforeBullet: 'Managed social media campaigns for clients',
      afterBullet:
        'Led 15+ multi-channel marketing campaigns for B2B clients, generating £2.3M in revenue and increasing lead conversion rates by 45%',
      atsScore: 96,
      keywords: 18
    },
    {
      profession: 'Software Developer',
      company: 'TechCorp London',
      beforeBullet: 'Built websites using JavaScript and React',
      afterBullet:
        'Developed 12+ responsive web applications using React.js, Node.js, and TypeScript, reducing page load times by 40% and improving user engagement by 25%',
      atsScore: 94,
      keywords: 16
    }
  ];

  // Auto-rotate examples every 8 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentExample((prev) => (prev + 1) % examples.length);
    }, 8000);
    return () => clearInterval(interval);
  }, [examples.length]);

  const activeExample = examples[currentExample];

  return (
    <div className="relative z-10 ">
      <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start">
          {/* Left Column - Features */}
          <div className="space-y-8">
            {/* Key Features - Enhanced Cards */}
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-white mb-6">
                Everything you need to land your next role
              </h3>

              <div className="grid gap-4">
                <div className="group p-6 bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm border border-white/10 rounded-2xl hover:border-hero-yellow/30 transition-all duration-300 hover:shadow-xl hover:shadow-hero-yellow/10">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-hero-yellow/20 rounded-xl">
                      <span className="w-6 h-6 bg-hero-yellow rounded animate-pulse block"></span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-white font-bold text-lg mb-2">
                        ATS-Optimised CVs & Cover Letters
                      </h4>
                      <p className="text-slate-300 text-sm leading-relaxed">
                        Generated and tailored in under 60 seconds with
                        industry-specific keywords
                      </p>
                    </div>
                  </div>
                </div>

                <div className="group p-6 bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm border border-white/10 rounded-2xl hover:border-hero-yellow/30 transition-all duration-300 hover:shadow-xl hover:shadow-hero-yellow/10">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-green-500/20 rounded-xl">
                      <Target className="w-6 h-6 text-green-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-white font-bold text-lg mb-2">
                        Success Rate Predictor
                      </h4>
                      <p className="text-slate-300 text-sm leading-relaxed">
                        Know your chances before you apply with AI-powered job
                        matching
                      </p>
                    </div>
                  </div>
                </div>

                <div className="group p-6 bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm border border-white/10 rounded-2xl hover:border-hero-yellow/30 transition-all duration-300 hover:shadow-xl hover:shadow-hero-yellow/10">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-purple-500/20 rounded-xl">
                      <span className="w-6 h-6 bg-purple-400 rounded animate-pulse block"></span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-white font-bold text-lg mb-2">
                        AI Mock Interview Practice
                      </h4>
                      <p className="text-slate-300 text-sm leading-relaxed">
                        Practice interviews online with real-time feedback and
                        personalized coaching
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-hero-yellow/10 to-orange-400/10 border border-hero-yellow/30 rounded-2xl p-6 backdrop-blur-sm">
                <div className="flex items-center gap-3 mb-3">
                  <span className="w-5 h-5 bg-yellow-400 rounded animate-pulse block"></span>
                  <span className="font-bold text-hero-yellow">
                    UK Market Specialist
                  </span>
                </div>
                <p className="text-slate-200 text-sm leading-relaxed">
                  Built specifically for the UK job market with ATS optimisation
                  for major UK employers including NHS, Civil Service, and FTSE
                  100 companies.
                </p>
              </div>
            </div>

            {/* Secondary CTAs */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="/onboarding"
                  className="inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-black bg-hero-yellow hover:bg-yellow-300 rounded-xl transition-colors"
                >
                  Optimise My CV Now
                </a>

                <a
                  href="/ats-resume-checker"
                  className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border border-white/20 hover:bg-white/10 rounded-xl transition-colors"
                >
                  Free ATS Check First
                </a>
              </div>

              <div className="flex items-center justify-center sm:justify-start gap-4 text-sm text-slate-400">
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  <span>No credit card required</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  <span>Free ATS analysis included</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Live Demo */}
          <div className="flex justify-center lg:justify-end">
            <div className="relative">
              {/* Enhanced background effect */}
              <div className="absolute inset-0 rounded-3xl blur-3xl scale-110 opacity-60 animate-pulse" />

              {/* Live CV Analysis Demo */}
              <div className="relative max-w-[400px] lg:max-w-[480px] bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-6 shadow-2xl  transition-all duration-500 ease-out hover:scale-105">
                {/* Header */}
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="ml-auto text-xs text-slate-400">
                    Live AI Analysis
                  </span>
                </div>

                {/* Demo title */}
                <div className="mb-4">
                  <h3 className="text-white font-bold text-lg mb-2">
                    🎯 {activeExample.profession}
                  </h3>
                  <div className="text-xs text-slate-400 mb-2">
                    {activeExample.company}
                  </div>
                  <div className="bg-hero-yellow/20 rounded-lg px-3 py-2">
                    <span className="text-hero-yellow font-semibold text-sm">
                      ATS Score: {activeExample.atsScore}% • Keywords:{' '}
                      {activeExample.keywords} • Format: ✓
                    </span>
                  </div>
                </div>

                {/* Before/After Example */}
                <div className="space-y-4 mb-6">
                  <div>
                    <div className="text-xs text-slate-400 mb-2">
                      ❌ BEFORE (Generic)
                    </div>
                    <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <span className="text-slate-300 text-sm">
                        &quot;{activeExample.beforeBullet}&quot;
                      </span>
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <div className="flex items-center gap-2 text-hero-yellow">
                      <span className="w-4 h-4 bg-purple-400 rounded animate-pulse block"></span>
                      <span className="text-xs">AI Enhancement</span>
                      <span className="w-4 h-4 bg-purple-400 rounded animate-pulse block"></span>
                    </div>
                  </div>

                  <div>
                    <div className="text-xs text-slate-400 mb-2">
                      ✅ AFTER (ATS-Optimized)
                    </div>
                    <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                      <span className="text-slate-300 text-sm">
                        &quot;{activeExample.afterBullet}&quot;
                      </span>
                    </div>
                  </div>
                </div>

                {/* Feature highlights */}
                <div className="grid grid-cols-2 gap-3 mb-4">
                  <div className="bg-white/5 rounded-lg p-3 text-center">
                    <span className="w-5 h-5 bg-yellow-400 rounded mx-auto mb-1 block"></span>
                    <div className="text-xs text-slate-300">ATS Format</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-3 text-center">
                    <Target className="w-5 h-5 text-green-400 mx-auto mb-1" />
                    <div className="text-xs text-slate-300">Keywords</div>
                  </div>
                </div>

                {/* Progress dots */}
                <div className="flex justify-center gap-2 mb-4">
                  {examples.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentExample(index)}
                      className={`transition-all duration-300 ${
                        index === currentExample
                          ? 'w-6 h-2 bg-hero-yellow rounded-full'
                          : 'w-2 h-2 bg-white/30 rounded-full hover:bg-white/50'
                      }`}
                      aria-label={`View ${examples[index].profession} example`}
                    />
                  ))}
                </div>

                {/* Live indicator */}
                <div className="flex items-center justify-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-slate-400">
                    Try it free - No signup required
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Trust Badges */}
        <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-slate-400 border-t border-white/10 pt-8 mt-12">
          <div className="flex items-center gap-2 hover:text-slate-300 transition-colors">
            <div className="relative h-5 w-16">
              <Image
                src="/images/third-party-logos/powered-by-stripe-white.svg"
                alt="Powered by Stripe"
                fill
                sizes="64px"
                style={{ objectFit: 'contain' }}
                priority={false}
              />
            </div>
          </div>

          <div className="flex items-center gap-2 hover:text-slate-300 transition-colors">
            <span className="w-4 h-4 bg-white rounded animate-pulse block"></span>
            <span>Secure checkout</span>
          </div>

          <span className="hover:text-slate-300 transition-colors">
            Powered by Google
          </span>
          <span className="hover:text-slate-300 transition-colors">
            🇬🇧 UK focused
          </span>
        </div>
      </div>
    </div>
  );
};

export default HeroBelowFold;
