import React, { useCallback } from 'react';
import {
  CheckCircle2,
  Plus,
  Sparkles,
  Rocket,
  Crown,
  ArrowRight,
  Zap
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ThemedButton } from '@/components/ui/themed-button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { User } from '@supabase/supabase-js';
import { creditPackages } from '@/lib/credit-packages';
import CreditBalance from '../credits/CreditBalance';
import CreditPackageCard from '../credits/CreditPackageCard';
import { PlanType } from '@/app/types/globalTypes';

export interface Product {
  title: string;
  features: string[];
  credits: number;
  maxJobs?: number;
  planType: PlanType;
  price: number;
  creditCost: number;
}

interface ProductSelectionProps {
  onProductSelect: (product: Product) => void;
  userCredits: number;
  onPurchaseCredits?: (packageId: string) => Promise<void>;
  user: User;
}

// Move products outside component to prevent recreation on each render
const products: Product[] = [
  {
    title: 'Basic',
    features: [
      'ATS Analysis with score & keyword matching',
      'Custom cover letter generation',
      'CV optimization recommendations',
      'Keyword matching analysis',
      'Missing skills identification'
    ],
    credits: 10,
    maxJobs: 1,
    planType: PlanType.BASIC,
    price: 10,
    creditCost: 10
  },
  {
    title: 'Pro',
    features: [
      'All Basic features',
      'Skills gap analysis with learning topics',
      'Personalized skill improvement resources',
      'Mock interview preparation',
      'Customized learning recommendations',
      'Priority Support'
    ],
    credits: 20,
    maxJobs: 1,
    planType: PlanType.PRO,
    price: 20,
    creditCost: 20
  },
  {
    title: 'Ultimate',
    features: [
      'All Pro features',
      'Career matching analysis',
      'AI Career Coach with personalized guidance',
      'Market trends insights',
      'Priority Support'
    ],
    credits: 50,
    maxJobs: 6,
    planType: PlanType.ULTIMATE,
    price: 50,
    creditCost: 50
  }
];

export const PLAN_FEATURES = {
  basic: {
    maxJobs: 1,
    features: [
      'ATS Analysis with score & keyword matching',
      'Custom cover letter generation',
      'CV optimization recommendations',
      'Keyword matching analysis',
      'Missing skills identification'
    ]
  },
  pro: {
    maxJobs: 1,
    features: [
      'All Basic features',
      'Skills gap analysis with learning topics',
      'Personalized skill improvement resources',
      'Mock interview preparation',
      'Customized learning recommendations',
      'Priority Support'
    ]
  },
  ultimate: {
    maxJobs: 6,
    features: [
      'All Pro features',
      'Career matching analysis',
      'AI Career Coach with personalized guidance',
      'Market trends insights',
      'Priority Support'
    ]
  }
};

const ProductSelection: React.FC<ProductSelectionProps> = ({
  onProductSelect,
  userCredits,
  onPurchaseCredits,
  user
}) => {
  const [selectedProduct, setSelectedProduct] = React.useState<Product | null>(
    null
  );
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [hoveredProduct, setHoveredProduct] = React.useState<string | null>(
    null
  );
  const [isButtonAnimating, setIsButtonAnimating] = React.useState<
    string | null
  >(null);
  const [isLoadingCredits, setIsLoadingCredits] = React.useState(false);
  const [creditsError, setCreditsError] = React.useState<string | null>(null);

  // Use useCallback for event handlers to prevent recreation on each render
  const handleSelect = useCallback(
    (product: Product) => {
      setSelectedProduct(product);
      setIsButtonAnimating(product.title);
      setTimeout(() => {
        setIsButtonAnimating(null);
      }, 800);

      // Make sure we're passing the correct plan type
      console.log(`Selected plan type: ${product.planType}`);

      // Call the parent component's onProductSelect handler
      onProductSelect(product);
    },
    [onProductSelect]
  );

  const hasEnoughCredits = useCallback(
    (product: Product) => {
      return userCredits >= product.credits;
    },
    [userCredits]
  );

  const getProductIcon = useCallback((title: string) => {
    switch (title) {
      case 'Basic':
        return <Sparkles className="h-6 w-6 text-[hsl(var(--hero-yellow))]" />;
      case 'Pro':
        return <Rocket className="h-6 w-6 text-[hsl(var(--hero-yellow))]" />;
      case 'Ultimate':
        return <Crown className="h-6 w-6 text-[hsl(var(--hero-yellow))]" />;
      default:
        return null;
    }
  }, []);

  const isRecommended = useCallback(
    (product: Product) => product.title === 'Pro',
    []
  );

  const handleDialogOpenChange = useCallback((open: boolean) => {
    setIsDialogOpen(open);
  }, []);

  const handleProductHover = useCallback((title: string | null) => {
    setHoveredProduct(title);
  }, []);

  // Use useEffect only once to fetch credits on mount
  React.useEffect(() => {
    const fetchCredits = async () => {
      try {
        setIsLoadingCredits(true);
        setCreditsError(null);
        // Simulate credit loading (replace with actual API call if needed)
        await new Promise((resolve) => setTimeout(resolve, 500));
      } catch (error) {
        setCreditsError(
          error instanceof Error ? error.message : 'Failed to load credits'
        );
      } finally {
        setIsLoadingCredits(false);
      }
    };

    fetchCredits();
  }, []); // Empty dependency array means this runs once on mount

  const getButtonContent = useCallback(
    (product: Product) => {
      if (selectedProduct?.title === product.title) {
        return (
          <>
            Selected <CheckCircle2 className="ml-2 h-4 w-4" />
          </>
        );
      }

      if (hasEnoughCredits(product)) {
        return (
          <>
            Select Plan <ArrowRight className="ml-2 h-4 w-4" />
          </>
        );
      }

      return (
        <>
          Need {product.credits - userCredits} More Credits{' '}
          <Zap className="ml-2 h-4 w-4" />
        </>
      );
    },
    [selectedProduct, hasEnoughCredits, userCredits]
  );

  return (
    <div className="pb-6 space-y-8">
      <div className="text-center space-y-4">
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-4">
          <Card className="px-6 py-3 backdrop-blur-sm bg-white/10 border border-white/20 text-white shadow-lg">
            <p className="text-sm font-medium">
              Available Credits:{' '}
              <span className="text-xl font-bold text-[hsl(var(--hero-yellow))]">
                {userCredits}
              </span>
            </p>
          </Card>
          <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
            <DialogTrigger asChild>
              <ThemedButton
                variant="outline"
                size="lg"
                className="hover:scale-105"
              >
                <Plus className="h-4 w-4 mr-2" />
                Buy Credits
              </ThemedButton>
            </DialogTrigger>
            <DialogContent className="max-w-4xl overflow-y-auto max-h-[90vh] sm:max-h-fit backdrop-blur-sm bg-hero-bg/90 border border-white/20 text-white">
              <DialogHeader className="text-center space-y-3">
                <DialogTitle className="text-3xl font-bold text-[hsl(var(--hero-yellow))]">
                  Purchase Credits
                </DialogTitle>
                <DialogDescription className="text-lg text-slate-300">
                  Choose a credit package to continue using our services
                </DialogDescription>
              </DialogHeader>

              <div className="max-w-md mx-auto my-4">
                <CreditBalance
                  credits={userCredits}
                  isLoading={isLoadingCredits}
                  error={creditsError}
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                {creditPackages.map((pkg) => (
                  <div key={pkg.id} className="relative">
                    <CreditPackageCard
                      package={pkg}
                      userId={user.id}
                      onPurchaseComplete={
                        onPurchaseCredits
                          ? () => onPurchaseCredits(pkg.id)
                          : undefined
                      }
                      isAuthenticated={true}
                    />
                  </div>
                ))}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        {products.map((product) => {
          const isProductRecommended = isRecommended(product);
          const isSelected = selectedProduct?.title === product.title;
          return (
            <div
              key={product.title}
              className={cn(
                'relative rounded-xl p-6 transition-all duration-300 flex flex-col h-full backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg',
                isProductRecommended &&
                  'border-[hsl(var(--hero-yellow))]/50 bg-[hsl(var(--hero-yellow))]/5',
                isSelected &&
                  'ring-2 ring-[hsl(var(--hero-yellow))] bg-[hsl(var(--hero-yellow))]/10 shadow-[0_0_15px_rgba(246,160,60,0.2)]',
                hoveredProduct === product.title && 'shadow-xl scale-[1.02]',
                !isSelected &&
                  'hover:border-[hsl(var(--hero-yellow))]/30 hover:shadow-[0_0_10px_rgba(246,160,60,0.15)]'
              )}
              onMouseEnter={() => handleProductHover(product.title)}
              onMouseLeave={() => handleProductHover(null)}
            >
              {isProductRecommended && (
                <div className="absolute -top-2.5 left-0 right-0 mx-auto w-fit px-3 py-1 bg-hero-bg border border-[hsl(var(--hero-yellow))]/30 text-[hsl(var(--hero-yellow))] text-xs font-medium rounded-full shadow-[0_0_10px_rgba(246,160,60,0.2)]">
                  Recommended
                </div>
              )}

              {isSelected && (
                <div className="absolute -top-2.5 right-4 bg-[hsl(var(--hero-yellow))] text-[#111827] text-xs font-medium rounded-full px-3 py-1 flex items-center gap-1 shadow-[0_0_10px_rgba(246,160,60,0.3)]">
                  <CheckCircle2 className="h-3 w-3" />
                  Selected
                </div>
              )}

              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-2.5">
                  <div className="p-2 rounded-full bg-[hsl(var(--hero-yellow))]/10 border border-[hsl(var(--hero-yellow))]/20">
                    {getProductIcon(product.title)}
                  </div>
                  <h3
                    className={cn(
                      'text-xl font-bold text-white',
                      isSelected && 'text-[hsl(var(--hero-yellow))]'
                    )}
                  >
                    {product.title}
                  </h3>
                </div>
                <div className="text-right">
                  <p className="text-sm text-slate-300">Credits</p>
                  <p
                    className={cn(
                      'text-xl font-bold',
                      isSelected
                        ? 'text-[hsl(var(--hero-yellow))]'
                        : 'text-white'
                    )}
                  >
                    {product.credits}
                  </p>
                </div>
              </div>

              <ul className="space-y-3 mb-auto">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle2
                      className={cn(
                        'h-4 w-4 shrink-0 mr-2 mt-0.5',
                        isSelected
                          ? 'text-[hsl(var(--hero-yellow))]'
                          : 'text-[hsl(var(--hero-yellow))]/60'
                      )}
                    />
                    <span
                      className={cn(
                        'text-sm',
                        isSelected ? 'text-white' : 'text-slate-300'
                      )}
                    >
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>

              <div className="mt-6 pt-3 border-t border-white/10">
                <ThemedButton
                  onClick={() => handleSelect(product)}
                  disabled={!hasEnoughCredits(product)}
                  variant={
                    isSelected || isButtonAnimating === product.title
                      ? 'primary'
                      : 'secondary'
                  }
                  fullWidth
                  className={cn(
                    'transition-all duration-200 hover:scale-105',
                    isButtonAnimating === product.title &&
                      'animate-select-pulse',
                    !hasEnoughCredits(product) && 'opacity-60'
                  )}
                >
                  {getButtonContent(product)}
                </ThemedButton>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default React.memo(ProductSelection);
