// Modified ResumeSelection.tsx for the funnel

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { ThemedButton } from '@/components/ui/themed-button';
import { Loader2, Upload, AlertCircle } from 'lucide-react';
import { Resume } from '@/app/types/globalTypes';
import { User } from '@supabase/supabase-js';
import { createClient } from '@/app/utils/supabase/client';
import { UploadResumeDialog } from '@/components/dashboard/UploadResumeDialog';
import { triggerResumeAnalysis } from '@/components/dashboard/dashboardUtils';
import { ResumeCard } from '../dashboard/ResumeCard';
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
// Alert imports removed as they're not needed

interface ResumeSelectionProps {
  user: User;
  onComplete: (resume: Resume | null) => void;
  selectedResume: Resume | null;
}

const ResumeSelection: React.FC<ResumeSelectionProps> = ({
  user,
  onComplete,
  selectedResume
}) => {
  const [resumes, setResumes] = useState<Resume[]>([]);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [, setCompletedSteps] = useState<string[]>([]);
  const [, setProgress] = useState(0);
  const [noResumeSelected, setNoResumeSelected] = useState(
    selectedResume === null
  );
  const supabase = createClient();

  const fetchResumes = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('user_resumes')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (data) {
        setResumes(data);
      }
    } catch (err) {
      console.error('Error fetching CVs:', err);
    }
  }, [supabase, user.id]);

  // Call fetchResumes on component mount
  useEffect(() => {
    fetchResumes();
  }, [fetchResumes]);

  const handleUploadComplete = async (resume: Resume) => {
    try {
      setIsAnalyzing(true);
      setProgress(0);
      setCompletedSteps([]);

      // Start with file upload step
      setProgress(33);
      setCompletedSteps(['fileUpload']);

      // Trigger analysis
      console.log('Starting CV analysis for:', resume.id);
      await triggerResumeAnalysis(resume);

      // Update progress for analysis step
      setProgress(66);
      setCompletedSteps(['fileUpload', 'resumeAnalysis']);

      // Fetch the updated resume with analysis
      const { data: updatedResume } = await supabase
        .from('user_resumes')
        .select('*')
        .eq('id', resume.id)
        .single();

      if (updatedResume) {
        setProgress(100);
        setCompletedSteps(['fileUpload', 'resumeAnalysis', 'complete']);
        onComplete(updatedResume);
        setNoResumeSelected(false);
      } else {
        // If can't fetch updated resume, use the original one
        onComplete(resume);
        setNoResumeSelected(false);
      }

      await fetchResumes();
    } catch (error) {
      console.error('Error handling upload completion:', error);
      toast({
        title: 'Error',
        description: 'Failed to analyze CV. Please try again.',
        variant: 'destructive'
      });
      onComplete(resume); // Still pass the resume even if analysis failed
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleDelete = async (resume: Resume) => {
    try {
      const { error } = await supabase
        .from('user_resumes')
        .delete()
        .eq('id', resume.id);

      if (error) throw error;

      await fetchResumes();
      if (selectedResume?.id === resume.id) {
        // If the deleted resume was selected, set to null
        onComplete(null);
        setNoResumeSelected(true);
      }
    } catch (err) {
      console.error('Error deleting CV:', err);
    }
  };

  const handleSelect = async (resume: Resume, isSelected: boolean) => {
    if (isSelected) {
      onComplete(resume);
      setNoResumeSelected(false);
    } else {
      // If deselecting, set to null
      onComplete(null);
      setNoResumeSelected(true);
    }
  };

  return (
    <div className=" mx-auto space-y-8">
      {/* Regular content */}
      {!isAnalyzing && (
        <>
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pb-6 border-b border-white/20">
            <div>
              <h2 className="text-2xl font-semibold text-white">
                Select or Upload{' '}
                <span className="text-[hsl(var(--hero-yellow))] text-shadow-yellow">
                  CV
                </span>
              </h2>
              <p className="text-slate-300 mt-1">
                Choose an existing CV or upload a new one to continue
              </p>
            </div>
            <ThemedButton
              onClick={() => setIsUploadDialogOpen(true)}
              variant="primary"
              className="w-full sm:w-auto"
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload CV
            </ThemedButton>
          </div>

          {/* Resume grid/list */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {resumes.map((resume) => (
              <ResumeCard
                key={resume.id}
                resume={resume}
                isSelected={selectedResume?.id === resume.id}
                onSelect={handleSelect}
                onDelete={handleDelete}
                className={cn(
                  'group relative rounded-lg border-2 p-4 transition-all duration-200 hover:shadow-md backdrop-blur-sm bg-white/10',
                  selectedResume?.id === resume.id
                    ? 'border-[hsl(var(--hero-yellow))] bg-[hsl(var(--hero-yellow))]/10 shadow-[0_0_10px_rgba(246,160,60,0.3)]'
                    : 'border-white/20 hover:border-[hsl(var(--hero-yellow))]/50 hover:shadow-[0_0_10px_rgba(246,160,60,0.2)]'
                )}
              />
            ))}
          </div>

          {/* Warning message when no resume is selected */}
          {noResumeSelected && (
            <div className="mt-4 p-4 backdrop-blur-sm bg-[hsl(var(--hero-yellow))]/10 border border-[hsl(var(--hero-yellow))]/30 rounded-md flex items-start gap-3 shadow-lg">
              <AlertCircle className="h-5 w-5 text-[hsl(var(--hero-yellow))] flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-white">CV Required</h4>
                <p className="text-slate-300 text-sm mt-1">
                  Please select a CV to continue. If you don&apos;t have one
                  yet, upload a new CV using the button above.
                </p>
              </div>
            </div>
          )}
        </>
      )}

      {/* Loading Progress */}
      {isAnalyzing && (
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-12 w-12 animate-spin text-[#F6A03C] mb-4" />
          <p className="text-white">Processing your CV...</p>
          <p className="text-slate-300 text-sm mt-2">This may take a moment</p>
        </div>
      )}

      {/* Upload Dialog using the dashboard's component */}
      <UploadResumeDialog
        isOpen={isUploadDialogOpen}
        onClose={() => setIsUploadDialogOpen(false)}
        onComplete={handleUploadComplete}
        userId={user.id}
      />
    </div>
  );
};

export default ResumeSelection;
