'use client';

import React, { useEffect, useState } from 'react';
import { ArrowRight } from 'lucide-react';
import type { User } from '@supabase/supabase-js';
import { createClient } from '@/app/utils/supabase/client';
import { ThemedLinkButton } from '@/components/ui/themed-button';

const CTAButton = () => {
  const [user, setUser] = useState<User | null>(null);
  const supabase = createClient();

  useEffect(() => {
    // Get initial auth state
    supabase.auth.getUser().then(({ data: { user } }) => {
      console.log('[CTAButton] Initial user state:', user);
      setUser(user);
    });

    // Subscribe to auth changes
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange((_event, session) => {
      console.log('[CTAButton] Auth state changed:', {
        event: _event,
        user: session?.user
      });
      setUser(session?.user || null);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [supabase.auth]);

  // Encode the return URL properly
  const returnUrl = encodeURIComponent('/application-funnel?step=1');

  const ctaConfig = {
    text: user ? 'Get Started' : 'Unlock My Career Potential',
    href: user ? '/application-funnel?step=1' : `/signin?next=${returnUrl}`
  };

  console.log('[CTAButton] Current config:', ctaConfig);

  return (
    <ThemedLinkButton
      href={ctaConfig.href}
      variant="primary"
      size="lg"
      showArrow={true}
      onClick={() =>
        console.log(
          '[CTAButton] Button clicked, redirecting to:',
          ctaConfig.href
        )
      }
    >
      {ctaConfig.text}
    </ThemedLinkButton>
  );
};

export default CTAButton;
