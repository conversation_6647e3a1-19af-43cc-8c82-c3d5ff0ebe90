// components/home/<USER>
'use client';

import { Star } from 'lucide-react';
import { motion } from 'framer-motion';
import { CTAButton } from '@/components/ui/themed-button';
import SectionHeading, { Highlight } from '../ui/section-heading';

export const PromoSection = () => {
  return (
    <section className="relative isolate py-16 overflow-hidden">
      <div className="relative z-10 mx-auto w-full px-4">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <span className="text-yellow-300 font-semibold">
            LIMITED TIME OFFER
          </span>
          <SectionHeading className="text-center max-w-4xl mx-auto">
            Start Your Journey to <Highlight>Career Success</Highlight> Today!
          </SectionHeading>
          <p className="text-lg max-w-4xl mx-auto text-slate-200 mb-8">
            Join now and gain instant access to our exclusive AI-driven
            features. Our inclusive platform is designed for everyone, with
            tools tailored to your specific career needs.
          </p>

          <motion.div
            className="mb-8"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <CTAButton
              href="/onboarding"
              size="lg"
              className="w-full max-w-[320px] md:max-w-full mx-auto text-[16px] md:text-[18px] font-semibold"
            >
              Sign Up for a Free Analysis
            </CTAButton>
            <p className="text-sm text-yellow-300 mt-3">
              ✨ Includes complimentary Skill-Gap Analysis to kickstart your
              career growth!
            </p>
          </motion.div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm">
            <div className="flex items-center gap-1 text-white">
              {Array(5)
                .fill(null)
                .map((_, i) => (
                  <Star
                    key={i}
                    className="w-4 h-4 fill-yellow-400 text-yellow-400"
                  />
                ))}
              <span className="ml-2">4.9/5 from 1000+ reviews</span>
            </div>
            <div className="text-slate-200">10,000+ active users</div>
            <div className="text-slate-200">84% success rate</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};
