import {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle
} from 'react';
import { Loader2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ResultsDisplay from './ResultsDisplay';
import {
  BasicPlanResult,
  ProPlanResult,
  UltimatePlanResult,
  PlanType,
  StepProps,
  ATSAnalysisResponse,
  CoverLetterResponse,
  SkillGap,
  SkillImprovementResponse,
  InterviewQuestionsResult,
  CareerMatchingResponse,
  CareerCoachingResponse,
  MarketTrendsResult
} from '@/app/types/globalTypes';
import { User } from '@supabase/supabase-js';
import { useCredits } from '@/hooks/useCredits';

// Define a ref type for the ResultsStep component
export interface ResultsStepRef {
  generateResults: () => Promise<void>;
}

// Define the type for stream events
type StreamEvent = {
  status: string;
  progress: number;
  error?: string;
  results?: BasicPlanResult | ProPlanResult | UltimatePlanResult;
  result?: BasicPlanResult | ProPlanResult | UltimatePlanResult; // Handle both result and results properties
  atsResult?: ATSAnalysisResponse;
  coverLetter?: CoverLetterResponse;
  skillsGap?: SkillGap[];
  skillImprovements?: SkillImprovementResponse;
  mockInterview?: InterviewQuestionsResult;
  careerMatches?: CareerMatchingResponse;
  careerCoaching?: CareerCoachingResponse;
  marketTrends?: MarketTrendsResult;
  planType?: PlanType;
  completedSteps?: string[];
};

export const ResultsStep = forwardRef<
  ResultsStepRef,
  StepProps & { user: User }
>(({ state, setState, user }, ref) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState<string>('');
  const [showDebugInfo, setShowDebugInfo] = useState(false);
  const [results, setResults] = useState<
    BasicPlanResult | ProPlanResult | UltimatePlanResult | null
  >(null);
  const { fetchCredits, cleanup } = useCredits();
  const resultsRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch credits when component mounts or unmounts
  useEffect(() => {
    fetchCredits();

    return () => {
      // Clean up any pending requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      // Clean up polling interval
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
      // Fetch credits again when component unmounts (user hits back)
      fetchCredits();
      // Clean up credit subscription when component unmounts
      cleanup();
    };
  }, [fetchCredits, cleanup]);

  // Scroll to results when they're generated
  useEffect(() => {
    if ((results || state.results) && resultsRef.current) {
      // Use setTimeout to ensure the DOM has updated
      setTimeout(() => {
        if (resultsRef.current) {
          resultsRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }, 100);
    }
  }, [results, state.results]);

  // Generate results function with streaming implementation
  const generateResults = async () => {
    console.log('[ResultsStep] Current funnel state:', {
      step: state.step,
      hasJob: !!state.job,
      job: state.job,
      jobId: state.job?.id,
      jobUserId: state.job?.user_id,
      hasResume: !!state.selectedResume,
      resumeId: state.selectedResume?.id,
      selectedProduct: {
        type: state.selectedProduct?.planType,
        cost: state.selectedProduct?.creditCost
      },
      credits: state.credits,
      processingResults: state.processingResults,
      hasExistingResults: !!state.results,
      hasGeneratedResults: !!state.generatedResults
    });

    // Validate required data before making the request
    if (!state.selectedResume?.id || !state.job?.id || !user?.id) {
      console.error('[ResultsStep] Missing required data:', {
        resumeId: state.selectedResume?.id,
        jobId: state.job?.id,
        userId: user?.id
      });
      setError(
        'Missing required information. Please ensure you have selected both a resume and a job.'
      );
      return;
    }

    const requestData = {
      resumeId: state.selectedResume.id,
      userId: user.id,
      job: state.job,
      jobDescription: state.job.description,
      planType: state.selectedProduct?.planType || PlanType.BASIC,
      creditsAmount: state.selectedProduct?.creditCost || 10,
      resumeData:
        state.selectedResume.content || state.selectedResume.analysis_result
    };

    console.log('[ResultsStep] Sending request with data:', requestData);

    try {
      setIsLoading(true);
      // Set initial progress and step to show something is happening
      setProgress(5);
      setCurrentStep('started');

      // Create a new AbortController for this request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_URL}/api/generate-results`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'text/event-stream'
          },
          body: JSON.stringify(requestData),
          signal: abortControllerRef.current.signal
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        console.error('[ResultsStep] API error response:', errorData);
        throw new Error(
          errorData.details || errorData.error || 'Failed to generate results'
        );
      }

      // Process the streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('ReadableStream not supported');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Decode the chunk and add it to our buffer
        buffer += decoder.decode(value, { stream: true });

        // Split the buffer by double newlines (SSE format)
        const chunks = buffer.split('\n\n');
        // Keep the last (potentially incomplete) chunk in the buffer
        buffer = chunks.pop() || '';

        for (const chunk of chunks) {
          // Skip empty chunks
          if (!chunk.trim()) continue;

          // Remove the "data: " prefix
          const cleanedChunk = chunk.replace(/^data:\s*/, '').trim();
          if (!cleanedChunk) continue;

          try {
            // Parse the JSON event
            const event = JSON.parse(cleanedChunk) as StreamEvent;

            console.log('[ResultsStep] Received stream event:', {
              status: event.status,
              progress: event.progress,
              hasResults: !!event.results,
              completedSteps: event.completedSteps,
              timestamp: new Date().toISOString()
            });

            // Update progress
            if (typeof event.progress === 'number') {
              setProgress(event.progress);
            }

            // Update current step
            if (event.status) {
              setCurrentStep(event.status);
            }

            // Handle error
            if (event.error) {
              throw new Error(event.error);
            }

            // Handle completed results
            if (
              event.status === 'completed' &&
              (event.results || event.result)
            ) {
              // Ensure the planType is consistent
              const finalResults = event.results || event.result;

              // Make sure we have valid results
              if (finalResults) {
                if (
                  !finalResults.planType ||
                  finalResults.planType !==
                    (state.selectedProduct?.planType || PlanType.BASIC)
                ) {
                  finalResults.planType =
                    state.selectedProduct?.planType || PlanType.BASIC;
                }

                // Set progress to 100%
                setProgress(100);

                // Set isLoading to false to show the results
                setIsLoading(false);

                // Set the results
                setResults(finalResults);

                // Update the parent state
                setState((prevState) => ({
                  ...prevState,
                  generatedResults: finalResults
                }));

                console.log('[ResultsStep] Results generation complete:', {
                  planType: finalResults.planType,
                  hasAtsResult: !!finalResults.atsResult,
                  hasCoverLetter: !!finalResults.coverLetter,
                  hasSkillsGap: !!(
                    finalResults as ProPlanResult | UltimatePlanResult
                  )?.skillsGap,
                  hasSkillImprovements: !!(
                    finalResults as ProPlanResult | UltimatePlanResult
                  )?.skillImprovements,
                  timestamp: new Date().toISOString()
                });
              } else {
                console.error(
                  '[ResultsStep] Received completed event but no results'
                );
              }
            }
          } catch (e) {
            console.error('[ResultsStep] Error parsing stream chunk:', e);
          }
        }
      }
    } catch (err) {
      if (err instanceof DOMException && err.name === 'AbortError') {
        console.log('Request was aborted intentionally');
      } else {
        console.error('Error generating results:', err);
        setError(
          err instanceof Error
            ? err.message.includes('{')
              ? JSON.parse(err.message).error
              : err.message
            : 'An unknown error occurred'
        );
      }
      setIsLoading(false);
    }
  };

  // Expose the generateResults function through the ref
  useImperativeHandle(ref, () => ({
    generateResults
  }));

  if (isLoading) {
    // Function to get a user-friendly step description
    const getStepDescription = (step: string): string => {
      switch (step) {
        case 'started':
          return 'Starting analysis...';
        case 'analyzing_ats':
          return 'Analyzing your resume against the job description...';
        case 'ats_complete':
          return 'ATS analysis complete. Generating cover letter...';
        case 'generating_cover_letter':
          return 'Creating your personalized cover letter...';
        case 'cover_letter_complete':
          return 'Cover letter complete. Analyzing skills gap...';
        case 'analyzing_skills_gap':
          return 'Analyzing your skills gap...';
        case 'skills_gap_complete':
          return 'Skills gap analysis complete. Generating skill improvements...';
        case 'generating_skill_improvements':
          return 'Creating your skill improvement recommendations...';
        case 'skill_improvements_complete':
          return 'Skill improvements complete. Analyzing market trends...';
        case 'analyzing_market_trends':
          return 'Analyzing market trends for your industry...';
        case 'market_trends_complete':
          return 'Market trends analysis complete. Finalizing results...';
        case 'completed':
          return 'Analysis complete! Preparing your results...';
        default:
          // Fallback to progress-based messages if no specific step is available
          return progress < 30
            ? 'Starting analysis...'
            : progress < 60
              ? 'Analyzing your resume against the job description...'
              : progress < 90
                ? 'Generating your personalized results...'
                : 'Almost done! Finalizing your results...';
      }
    };

    // Get the current step description
    const stepDescription = getStepDescription(currentStep);

    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-6 min-h-[400px] backdrop-blur-sm bg-white/5 border border-white/10 rounded-lg shadow-lg">
        <Loader2 className="h-16 w-16 animate-spin text-[hsl(var(--hero-yellow))]" />
        <div className="text-center space-y-3">
          <h3 className="text-2xl font-semibold text-white">
            Analyzing Your Application
          </h3>
          <p className="text-slate-300 text-lg">{stepDescription}</p>
          <div className="w-full max-w-md mx-auto bg-white/10 h-3 rounded-full overflow-hidden mt-6">
            <div
              className="bg-[hsl(var(--hero-yellow))] h-full rounded-full transition-all duration-300 ease-in-out shadow-[0_0_8px_rgba(246,160,60,0.5)]"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <p className="text-base text-white mt-3">{progress}% complete</p>
          {currentStep && (
            <p className="text-sm text-slate-300 mt-1">
              Current step: {currentStep.replace(/_/g, ' ')}
            </p>
          )}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-6 min-h-[400px] backdrop-blur-sm bg-white/5 border border-white/10 rounded-lg shadow-lg">
        <AlertCircle className="h-16 w-16 text-red-400" />
        <div className="text-center space-y-3">
          <h3 className="text-2xl font-semibold text-red-400">Error</h3>
          <p className="text-white">{error}</p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center mt-6">
            <Button
              variant="outline"
              className="border-[hsl(var(--hero-yellow))] bg-[hsl(var(--hero-yellow))]/10 text-[hsl(var(--hero-yellow))] hover:bg-[hsl(var(--hero-yellow))]/20 hover:text-[hsl(var(--hero-yellow))] hover:border-[hsl(var(--hero-yellow))] hover:shadow-[0_0_10px_rgba(246,160,60,0.3)] transition-all duration-200"
              onClick={() => {
                setError(null);
                setIsLoading(false);
              }}
            >
              Try Again
            </Button>
            <Button
              variant="ghost"
              className="text-white/70 hover:text-white hover:bg-white/10"
              onClick={() => setShowDebugInfo(!showDebugInfo)}
            >
              {showDebugInfo ? 'Hide Debug Info' : 'Show Debug Info'}
            </Button>
          </div>

          {showDebugInfo && (
            <div className="mt-6">
              <div className="text-sm text-left mb-2 font-medium text-[hsl(var(--hero-yellow))]">
                Debug Information:
              </div>
              <div className="text-left bg-black/30 border border-white/10 p-4 rounded-md text-xs overflow-auto max-h-48 text-slate-300">
                <pre>
                  User ID: {user?.id || 'Unknown'}
                  <br />
                  Resume ID: {state.selectedResume?.id || 'Unknown'}
                  <br />
                  Job ID: {state.job?.id || 'Unknown'} (Type:{' '}
                  {typeof state.job?.id})
                  <br />
                  Job User ID: {state.job?.user_id || 'Unknown'}
                  <br />
                  Plan Type: {state.selectedProduct?.planType || 'BASIC'}
                  <br />
                  Job Status: {state.job?.status || 'Unknown'}
                  <br />
                  Job Title: {state.job?.title || 'Unknown'}
                  <br />
                  Credits: {state.credits || 'Unknown'}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6" ref={resultsRef}>
      {results ? (
        <ResultsDisplay
          results={
            results ||
            (state.results as
              | BasicPlanResult
              | ProPlanResult
              | UltimatePlanResult)
          }
          selectedPlan={state.selectedProduct?.planType || PlanType.BASIC}
          job={state.job!}
          resume={state.selectedResume!}
        />
      ) : (
        <div className="p-8 backdrop-blur-sm bg-white/10 border border-white/20 rounded-lg shadow-lg text-center">
          <h2 className="text-2xl font-bold mb-4 text-white">
            No Results Available
          </h2>
          <p className="mb-6 text-slate-300">
            Click the button below to generate your application results.
          </p>
          <Button
            onClick={generateResults}
            className="bg-[hsl(var(--hero-yellow))] text-[hsl(var(--foreground))] hover:bg-[hsl(var(--hero-yellow-light))] active:scale-95 transition-all px-6"
          >
            Generate Results
          </Button>

          {/* API Test Component */}
          <div className="mt-8 border-t border-white/20 pt-4">
            <h3 className="text-lg font-semibold mb-4 text-[hsl(var(--hero-yellow))]">
              API Endpoint Testing
            </h3>
            <div className="text-left">
              <p className="text-sm text-slate-300 mb-2">
                If you&apos;re having issues, you can test the API endpoints
                directly:
              </p>
              <div className="mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-[hsl(var(--hero-yellow))] bg-[hsl(var(--hero-yellow))]/10 text-[hsl(var(--hero-yellow))] hover:bg-[hsl(var(--hero-yellow))]/20 hover:text-[hsl(var(--hero-yellow))] hover:border-[hsl(var(--hero-yellow))] hover:shadow-[0_0_10px_rgba(246,160,60,0.3)] transition-all duration-200"
                  onClick={async () => {
                    try {
                      const response = await fetch('/api/test', {
                        method: 'GET'
                      });
                      const data = await response.json();
                      alert(`Test API response: ${JSON.stringify(data)}`);
                    } catch (error) {
                      alert(
                        `Test API error: ${error instanceof Error ? error.message : String(error)}`
                      );
                    }
                  }}
                >
                  Test API Connection
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

// Add displayName for better debugging
ResultsStep.displayName = 'ResultsStep';
