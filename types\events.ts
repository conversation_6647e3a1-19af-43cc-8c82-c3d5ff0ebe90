// types/events.ts
// ✅ Clean separation of event types to avoid index signature conflicts

// Base event parameters that all events can have
export interface BaseEventParams {
  event_category?: string;
  event_label?: string;
  value?: number;
  page_path?: string;
  page_title?: string;
  page_location?: string;
  user_id?: string;
  session_id?: string;
  engagement_time_msec?: number;
  screen_name?: string;
}

// Simple events that only use primitive types
export interface SimpleEventParams extends BaseEventParams {
  // Using a more specific index signature to avoid conflicts
  custom_parameter?: string;
  [key: `custom_${string}`]: string | number | boolean | undefined;
}

// Item interface for e-commerce
export interface GtagItem {
  item_id?: string;
  item_name?: string;
  item_category?: string;
  item_category2?: string;
  item_category3?: string;
  item_category4?: string;
  item_category5?: string;
  item_brand?: string;
  item_variant?: string;
  price?: number;
  quantity?: number;
  index?: number;
  coupon?: string;
  item_list_id?: string;
  item_list_name?: string;
  location_id?: string;
}

// E-commerce specific events
export interface PurchaseEventParams extends BaseEventParams {
  transaction_id: string;
  value: number;
  currency: string;
  items?: GtagItem[];
  coupon?: string;
  shipping?: number;
  tax?: number;
}

export interface AddToCartEventParams extends BaseEventParams {
  currency: string;
  value: number;
  items: GtagItem[];
}

export interface ViewItemListEventParams extends BaseEventParams {
  item_list_id?: string;
  item_list_name?: string;
  items: GtagItem[];
}

export interface ViewItemEventParams extends BaseEventParams {
  currency?: string;
  value?: number;
  items: GtagItem[];
}

export interface BeginCheckoutEventParams extends BaseEventParams {
  currency: string;
  value: number;
  items: GtagItem[];
  coupon?: string;
}

export interface RemoveFromCartEventParams extends BaseEventParams {
  currency: string;
  value: number;
  items: GtagItem[];
}

// Conversion events
export interface ConversionEventParams extends BaseEventParams {
  conversion_id?: string;
  conversion_label?: string;
  conversion_value?: number;
  conversion_currency?: string;
}

// Search events
export interface SearchEventParams extends BaseEventParams {
  search_term: string;
  items?: GtagItem[];
}

// Custom events with flexible parameters
export interface CustomEventParams extends BaseEventParams {
  custom_parameter_1?: string;
  custom_parameter_2?: string;
  custom_parameter_3?: string;
  // Common custom parameters
  button_location?: string;
  button_text?: string;
  page_section?: string;
  visitor_type?: string;
  conversion_intent?: string;
  cta_clicked?: string;
  visitor_engagement?: string;
  // Allow any custom_ prefixed parameters
  [key: `custom_${string}`]: string | number | boolean | undefined;
}

// Union type for all possible event parameters
export type AllEventParams =
  | SimpleEventParams
  | PurchaseEventParams
  | ViewItemListEventParams
  | ViewItemEventParams
  | BeginCheckoutEventParams
  | ConversionEventParams
  | SearchEventParams
  | CustomEventParams;

// Event name mapping for type safety
export interface EventTypeMap {
  page_view: SimpleEventParams;
  click: SimpleEventParams;
  scroll: SimpleEventParams;
  purchase: PurchaseEventParams;
  view_item_list: ViewItemListEventParams;
  view_item: ViewItemEventParams;
  begin_checkout: BeginCheckoutEventParams;
  search: SearchEventParams;
  conversion: ConversionEventParams;
  custom_event: CustomEventParams;
  // Allow any string for custom events
  [key: string]: AllEventParams;
}

// Type-safe event tracking function signature
export type TrackEventFunction = <T extends keyof EventTypeMap>(
  eventName: T,
  parameters?: EventTypeMap[T]
) => void;

// Helper type for extracting event parameters by event name
export type EventParamsFor<T extends keyof EventTypeMap> = EventTypeMap[T];

// Usage examples for documentation:
/*
  // ✅ Type-safe usage examples:
  
  // Simple event
  trackEvent('click', {
    event_category: 'button',
    event_label: 'signup',
    value: 1
  });
  
  // E-commerce event
  trackEvent('purchase', {
    transaction_id: 'tx_123',
    value: 29.99,
    currency: 'USD',
    items: [{
      item_id: 'product_123',
      item_name: 'Premium Plan',
      price: 29.99,
      quantity: 1
    }]
  });
  
  // Custom event with custom parameters
  trackCustomEvent('custom_event', {
    event_category: 'user_action',
    button_location: 'header',
    page_section: 'navigation',
    value: 42
  });
  */
