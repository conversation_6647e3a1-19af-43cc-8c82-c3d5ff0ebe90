'use client';

import { But<PERSON> } from '@/components/ui/button';
import { requestPasswordUpdate } from '@/app/utils/auth-helpers/server';
import { handleRequest } from '@/app/utils/auth-helpers/client';
import { useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Input } from '../input';
import { AlertCircle, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import FormAlert from './FormAlert';

// Define prop type
interface ForgotPasswordProps {
  disableButton?: boolean;
  allowEmail?: boolean;
  redirectMethod?: string;
}

export default function ForgotPassword({
  disableButton,
  /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
  ...unusedProps
}: Readonly<ForgotPasswordProps>) {
  // unusedProps contains allowEmail and redirectMethod which are required by the parent component but not used here
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [sentToEmail, setSentToEmail] = useState('');
  const { toast } = useToast();

  // Get status and error messages from URL parameters
  const status = searchParams.get('status');
  const statusDescription = searchParams.get('status_description');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');
  const emailSentParam = searchParams.get('email_sent');

  // Set email sent state based on URL parameter
  useEffect(() => {
    if (emailSentParam) {
      setEmailSent(true);
      setSentToEmail(emailSentParam);
    }
  }, [emailSentParam]);

  // Show toast notifications for errors or success messages
  useEffect(() => {
    if (error && errorDescription) {
      toast({
        title: error,
        description: errorDescription,
        variant: 'destructive'
      });
    } else if (status && statusDescription) {
      toast({
        title: status,
        description: statusDescription,
        variant: 'default'
      });
    }
  }, [error, errorDescription, status, statusDescription, toast]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    setIsSubmitting(true); // Disable the button while the request is being handled

    try {
      // Skip password validation since we're only requesting a password reset
      await handleRequest(e, requestPasswordUpdate, {
        skipPasswordValidation: true
      });

      // We don't need to set the state here as the page will refresh
      // and the state will be set from the URL parameters
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to send reset email';

      // Check for rate limit error
      if (errorMessage.toLowerCase().includes('rate limit')) {
        toast({
          title: 'Too Many Requests',
          description:
            'You have requested too many password resets. Please wait 60 minutes before trying again.',
          variant: 'destructive'
        });
      } else {
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive'
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTryAgain = () => {
    // Clear the email sent state
    setEmailSent(false);

    // Remove the email_sent parameter from the URL
    const url = new URL(window.location.href);
    url.searchParams.delete('email_sent');
    window.history.replaceState({}, '', url);
  };

  return (
    <div className="my-8 text-white">
      {/* Use FormAlert component */}
      <FormAlert
        status={status}
        statusDescription={statusDescription}
        error={error}
        errorDescription={errorDescription}
        showEmailSentUI={emailSent}
        sentToEmail={sentToEmail}
        onTryAgain={handleTryAgain}
      />

      {!emailSent && (
        <form
          noValidate={true}
          className="mb-4"
          onSubmit={(e) => handleSubmit(e)}
        >
          <div className="grid gap-2">
            <p className="text-sm text-white/80 mb-2">
              Please enter your email address below. We&apos;ll send you a link
              to reset your password.
            </p>
            <p className="text-xs text-amber-300 mb-2 flex items-center">
              <AlertCircle className="h-3 w-3 mr-1 inline" />
              Note: For security reasons, you can only request a password reset
              a limited number of times per hour.
            </p>
            <div className="grid gap-1">
              <label htmlFor="email" className="text-white">
                Email
              </label>
              <Input
                id="email"
                placeholder="<EMAIL>"
                type="email"
                name="email"
                autoCapitalize="none"
                autoComplete="email"
                autoCorrect="off"
                className="w-full p-3 rounded-md bg-white/10 border-white/20 text-white auth-input"
              />
            </div>
            <Button
              type="submit"
              className="mt-1 bg-[hsl(var(--hero-yellow))] hover:bg-[hsl(var(--hero-yellow-light))] text-[hsl(var(--foreground))]"
              loading={isSubmitting}
              disabled={disableButton}
            >
              Send Email
            </Button>
          </div>
        </form>
      )}

      {/* Note about password reset link expiration */}
      <div className="mt-6 text-xs text-white/60 flex items-center">
        <Clock className="h-3 w-3 mr-1 inline" />
        <span>Password reset links are valid for 10 minutes only.</span>
      </div>
    </div>
  );
}
