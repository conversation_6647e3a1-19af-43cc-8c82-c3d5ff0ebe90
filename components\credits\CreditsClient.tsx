'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { User } from '@supabase/supabase-js';
import { CreditPackage } from '@/app/types/globalTypes';
import { createClient } from '@/app/utils/supabase/client';
import { CreditPackageCard } from './CreditPackageCard';
import { CreditBalance } from './CreditBalance';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { ThemedButton } from '@/components/ui/themed-button';
import { Loader2, ArrowRight } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import { trackPricingPageView } from '@/lib/ga-events';

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
};

interface CreditsClientProps {
  user: User | null;
  initialCredits: number;
  creditPackages: CreditPackage[];
  initialError?: string;
}

export default function CreditsClient({
  user,
  initialCredits,
  creditPackages,
  initialError
}: Readonly<CreditsClientProps>) {
  const router = useRouter();
  const pathname = usePathname();
  const [credits, setCredits] = useState(initialCredits);
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingSession, setIsCheckingSession] = useState(true);
  const [currentUser, setCurrentUser] = useState<User | null>(user);
  const [error, setError] = useState<string | null>(initialError ?? null);

  useEffect(() => {
    trackPricingPageView();
  }, []);

  // Check user session on mount
  useEffect(() => {
    const checkSession = async () => {
      try {
        const supabase = createClient();
        const {
          data: { session }
        } = await supabase.auth.getSession();

        if (!session) {
          setCurrentUser(null);
        }
      } catch (error) {
        console.error('Error checking session:', error);
        setCurrentUser(null);
      } finally {
        setIsCheckingSession(false);
      }
    };

    if (user) {
      checkSession();
    } else {
      setIsCheckingSession(false);
    }
  }, [user]);

  const refreshCredits = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const supabase = createClient();
      const { data, error } = await supabase
        .from('credits')
        .select('amount')
        .eq('user_id', user.id)
        .single();

      if (error) throw error;
      setCredits(data.amount);
      setError(null);
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Failed to fetch credits');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (!user) return;

    const supabase = createClient();
    const channelName = `credits_changes_${user.id}`;

    // Setup function to create and subscribe to the channel
    const setupChannel = () => {
      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'credits',
            filter: `user_id=eq.${user.id}`
          },
          (payload) => {
            // Only update if the new value is different
            if (payload.new.amount !== credits) {
              setCredits(payload.new.amount);
            }
          }
        )
        .subscribe();

      return channel;
    };

    // Create the initial channel
    const channel = setupChannel();

    // Store channel name in sessionStorage for reconnection after bfcache restoration
    sessionStorage.setItem('credits-client-channel', channelName);

    // Handle visibility changes for bfcache compatibility
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // Page is being hidden - might go into bfcache
        // Remove all channels to make page eligible for bfcache
        supabase.removeAllChannels();
      } else if (document.visibilityState === 'visible') {
        // Page is becoming visible again - might be restored from bfcache
        // Reconnect the channel and refresh data
        setupChannel();
        refreshCredits();
      }
    };

    // Handle page hide event
    const handlePageHide = () => {
      supabase.removeAllChannels();
    };

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('pagehide', handlePageHide);

    return () => {
      // Clean up
      supabase.removeChannel(channel);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('pagehide', handlePageHide);
    };
  }, [user?.id, user, credits, refreshCredits]);

  const handleSignIn = () => {
    // Set returnTo cookie with path and expiration
    document.cookie = `returnTo=${pathname}; path=/; max-age=300`; // 5 minutes
    router.push('/signin');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="space-y-4 text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p className="text-muted-foreground">Loading your credits...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-12">
        <Alert variant="destructive" className="max-w-md mx-auto">
          <AlertDescription className="flex flex-col items-center space-y-4">
            <p>{error}</p>
            <Button variant="outline" onClick={refreshCredits} className="mt-2">
              Try again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="relative w-full overflow-hidden">
      <div className="py-12 sm:py-16 relative">
        <div className="text-center mb-12 space-y-6 px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <span className="text-yellow-300 font-semibold">PRICING</span>
            <h2 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2.5rem,8vw,4.5rem)]">
              Power Up Your{' '}
              <span className="text-yellow-400 text-shadow-yellow">Career</span>
            </h2>
            <p className="text-slate-200 max-w-2xl mx-auto text-[16px] md:text-[18px]">
              Select the perfect credit package for your job search journey.
              Each credit unlocks powerful AI-driven features.
            </p>

            <p className="text-yellow-300 italic max-w-2xl mx-auto text-[14px] md:text-[16px] mt-4">
              Get more interviews with AI-tailored CVs and cover letters that
              match every job description.
            </p>
          </motion.div>
        </div>

        <motion.div
          variants={item}
          initial="hidden"
          animate="show"
          className="max-w-[480px] mx-auto mb-12 px-4"
        >
          {isCheckingSession ? (
            <div className="flex items-center justify-center min-h-[200px]">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : currentUser ? (
            <CreditBalance
              credits={credits}
              isLoading={isLoading}
              error={error}
            />
          ) : (
            <Card className="relative overflow-hidden backdrop-blur-sm bg-white/10 border border-white/20 text-white">
              <CardContent className="p-8 text-center space-y-4 relative">
                <h2 className="text-2xl font-roboto-condensed font-bold text-white">
                  Get Started Today
                </h2>
                <p className="text-slate-200">
                  Sign in to unlock premium features and start your enhanced
                  career journey
                </p>
                <ThemedButton
                  size="lg"
                  onClick={handleSignIn}
                  variant="primary"
                  showArrow
                  className="px-8 py-4 md:py-5 font-semibold text-[16px] md:text-[18px] whitespace-nowrap"
                >
                  Sign In to Begin
                </ThemedButton>
              </CardContent>
            </Card>
          )}
        </motion.div>

        <motion.div
          variants={container}
          initial="hidden"
          animate="show"
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto px-4"
        >
          {creditPackages.map((pkg) => (
            <motion.div key={pkg.id} variants={item} className="relative group">
              <div className="absolute inset-0 transition-all duration-200 group-hover:shadow-lg rounded-lg" />
              <div className="relative">
                <CreditPackageCard
                  package={pkg}
                  userId={user?.id}
                  onPurchaseComplete={async () => {
                    await refreshCredits();
                  }}
                  isAuthenticated={!!user}
                />
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
}
